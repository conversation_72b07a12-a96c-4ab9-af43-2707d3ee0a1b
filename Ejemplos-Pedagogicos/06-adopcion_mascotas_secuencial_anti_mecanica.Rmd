---
title: "Adopción de Mascotas - Pregunta Secuencial Anti-Mecánica"
output: 
  exams::exams2moodle:
    name: "adopcion_mascotas_secuencial"
---

```{r inicio, include=FALSE}
library(exams)
library(digest)
library(testthat)

typ <- match_exams_device()
options(scipen=999)
knitr::opts_chunk$set(warning = FALSE, message = FALSE)
```

```{r data_generation, echo=FALSE, results="hide"}
# GENERACIÓN DE DATOS SECUENCIALES DEPENDIENTES

# Vector ampliado de mascotas para mayor diversidad
mascotas <- c('loro', 'perro', 'gato', 'gallina', 'hamster', 'cerdo', 'ternero', 
              'caballo', 'cabra', 'conejo', 'tortuga', 'pez', 'canario', 'hurón', 
              'iguana', 'serpiente', 'ratón', 'araña', 'camaleón', 'pato', 'pavo', 
              'oveja', 'vaca', 'burro', 'ganso', 'paloma', 'cisne', 'lagarto', 'erizo')

# Seleccionar 3 mascotas aleatoriamente
set.seed(sample(1:10000, 1))
selmascota <- sample(mascotas, 3)
nombremascota1 <- selmascota[1]
nombremascota2 <- selmascota[2] 
nombremascota3 <- selmascota[3]

# Número de encuestados (evitando números "fáciles")
numeros_posibles <- seq(120, 480, 15)  # Números más complejos
enkuestados <- sample(numeros_posibles, 1)

# FUNCIÓN MEJORADA PARA GENERAR PORCENTAJES ÚNICOS
generar_porcentajes_complejos <- function() {
  repetir <- TRUE
  intentos <- 0
  max_intentos <- 1000
  
  while (repetir && intentos < max_intentos) {
    intentos <- intentos + 1
    
    # Generar porcentajes que NO sean múltiplos de 5 para mayor dificultad
    primer_numero <- sample(c(12, 13, 17, 18, 22, 23, 27, 28, 32, 33, 37, 38, 42, 43, 47, 48), 1)
    
    # Calcular rango para segundo número
    max_segundo <- 88 - primer_numero
    min_segundo <- 12
    
    if(max_segundo >= min_segundo) {
      posibles_segundos <- min_segundo:max_segundo
      posibles_segundos <- posibles_segundos[posibles_segundos != primer_numero]
      
      if(length(posibles_segundos) > 0) {
        segundo_numero <- sample(posibles_segundos, 1)
        tercer_numero <- 100 - primer_numero - segundo_numero
        
        # Verificar que todos sean únicos y positivos
        if(tercer_numero > 0 && 
           length(unique(c(primer_numero, segundo_numero, tercer_numero))) == 3) {
          repetir <- FALSE
        }
      }
    }
  }
  
  if(repetir) {
    # Fallback a porcentajes simples si no se encuentra solución
    return(c(30, 35, 35))
  }
  
  return(c(primer_numero, segundo_numero, tercer_numero))
}

# Generar porcentajes
vector_porcentajes <- generar_porcentajes_complejos()
porcentaje1 <- vector_porcentajes[1]
porcentaje2 <- vector_porcentajes[2]
porcentaje3 <- vector_porcentajes[3]

# CÁLCULOS DEPENDIENTES PARA PREGUNTAS SECUENCIALES
# Parte A: Número absoluto de personas por mascota
personas_mascota1 <- round((enkuestados * porcentaje1) / 100)
personas_mascota2 <- round((enkuestados * porcentaje2) / 100)
personas_mascota3 <- round((enkuestados * porcentaje3) / 100)

# Parte B: Análisis comparativo (depende de Parte A)
mascota_mas_popular <- which.max(c(personas_mascota1, personas_mascota2, personas_mascota3))
nombre_mas_popular <- selmascota[mascota_mas_popular]
cantidad_mas_popular <- max(personas_mascota1, personas_mascota2, personas_mascota3)

diferencia_primera_segunda <- abs(sort(c(personas_mascota1, personas_mascota2, personas_mascota3), decreasing = TRUE)[1] - 
                                 sort(c(personas_mascota1, personas_mascota2, personas_mascota3), decreasing = TRUE)[2])

# Parte C: Proyección futura (depende de A y B)
# Si se duplicara el número de encuestados
encuestados_doble <- enkuestados * 2
proyeccion_mas_popular <- cantidad_mas_popular * 2

# DISTRACTORES INTELIGENTES PARA CADA PARTE

# Distractores Parte A (errores comunes en cálculo de porcentajes)
dist_a1_1 <- round(enkuestados * porcentaje1 / 10)  # Error: dividir por 10 en lugar de 100
dist_a1_2 <- round(enkuestados + porcentaje1)       # Error: sumar en lugar de multiplicar
dist_a1_3 <- round(enkuestados - porcentaje1)       # Error: restar en lugar de multiplicar

# Distractores Parte B (errores en análisis comparativo)
nombres_ordenados <- selmascota[order(c(personas_mascota1, personas_mascota2, personas_mascota3), decreasing = TRUE)]
dist_b1 <- nombres_ordenados[2]  # Segunda más popular
dist_b2 <- nombres_ordenados[3]  # Tercera más popular
dist_b3 <- "Todas por igual"     # Respuesta evasiva

# Distractores Parte C (errores en proyección)
dist_c1 <- proyeccion_mas_popular + enkuestados  # Error: sumar encuestados originales
dist_c2 <- proyeccion_mas_popular / 2            # Error: dividir en lugar de multiplicar
dist_c3 <- cantidad_mas_popular + enkuestados    # Error: sumar encuestados a cantidad original
```

## Análisis Secuencial: Programa de Adopción de Mascotas

**CONTEXTO GENERAL:** Un programa de adopción de mascotas realizó una encuesta a `r enkuestados` personas para conocer sus preferencias. Los resultados mostraron que:
- El `r porcentaje1`% prefiere adoptar un `r nombremascota1`
- El `r porcentaje2`% prefiere adoptar un `r nombremascota2`  
- El `r porcentaje3`% prefiere adoptar un `r nombremascota3`

---

### **PARTE A: Análisis Cuantitativo Básico**

**IMPORTANTE:** *Esta respuesta es fundamental para las siguientes preguntas*

Basándose en los porcentajes dados, ¿cuántas personas de las `r enkuestados` encuestadas prefieren adoptar un `r nombremascota1`?

```{r parte_a_opciones, echo=FALSE, results="asis"}
opciones_a <- c(
  personas_mascota1,
  dist_a1_1,
  dist_a1_2,
  dist_a1_3
)

# Asegurar que todas las opciones sean diferentes
opciones_a <- unique(opciones_a)
while(length(opciones_a) < 4) {
  nuevo_distractor <- sample(50:200, 1)
  if(!nuevo_distractor %in% opciones_a) {
    opciones_a <- c(opciones_a, nuevo_distractor)
  }
}

opciones_a <- sample(opciones_a[1:4])
pos_correcta_a <- which(opciones_a == personas_mascota1)

for(i in 1:4) {
  cat(LETTERS[i], ") ", opciones_a[i], " personas\n", sep="")
}
```

---

### **PARTE B: Análisis Comparativo Avanzado**

**IMPORTANTE:** *Esta pregunta requiere la respuesta correcta de la Parte A*

Si sabemos que `r personas_mascota1` personas prefieren `r nombremascota1`, `r personas_mascota2` personas prefieren `r nombremascota2`, y `r personas_mascota3` personas prefieren `r nombremascota3`, ¿cuál es la mascota MÁS popular y cuál es la diferencia con la segunda más popular?

```{r parte_b_opciones, echo=FALSE, results="asis"}
opciones_b <- c(
  paste0(nombre_mas_popular, " (diferencia: ", diferencia_primera_segunda, " personas)"),
  paste0(dist_b1, " (diferencia: ", diferencia_primera_segunda + 5, " personas)"),
  paste0(dist_b2, " (diferencia: ", diferencia_primera_segunda - 3, " personas)"),
  paste0(dist_b3, " (diferencia: 0 personas)")
)

opciones_b <- sample(opciones_b)
pos_correcta_b <- which(grepl(nombre_mas_popular, opciones_b) & grepl(as.character(diferencia_primera_segunda), opciones_b))

for(i in 1:4) {
  cat(LETTERS[i], ") ", opciones_b[i], "\n", sep="")
}
```

---

### **PARTE C: Proyección y Síntesis**

**IMPORTANTE:** *Esta pregunta requiere las respuestas correctas de las Partes A y B*

Si el programa decide duplicar el tamaño de la encuesta (de `r enkuestados` a `r encuestados_doble` personas) manteniendo las mismas proporciones, ¿cuántas personas preferirían la mascota más popular (`r nombre_mas_popular`)?

```{r parte_c_opciones, echo=FALSE, results="asis"}
opciones_c <- c(
  proyeccion_mas_popular,
  dist_c1,
  dist_c2,
  dist_c3
)

# Asegurar unicidad
opciones_c <- unique(opciones_c)
while(length(opciones_c) < 4) {
  nuevo_distractor <- sample(200:800, 1)
  if(!nuevo_distractor %in% opciones_c) {
    opciones_c <- c(opciones_c, nuevo_distractor)
  }
}

opciones_c <- sample(opciones_c[1:4])
pos_correcta_c <- which(opciones_c == proyeccion_mas_popular)

for(i in 1:4) {
  cat(LETTERS[i], ") ", opciones_c[i], " personas\n", sep="")
}
```

---

### **PARTE D: Reflexión Conceptual**

**PREGUNTA ABIERTA:** Explique en máximo 50 palabras por qué es importante analizar tanto los porcentajes como las cantidades absolutas en este tipo de estudios estadísticos:

{1:ESSAY}

---

## Solution

### **Solución Parte A:**
Para calcular el número de personas que prefieren `r nombremascota1`:
- Fórmula: (`r enkuestados` × `r porcentaje1`%) ÷ 100
- Cálculo: (`r enkuestados` × `r porcentaje1`) ÷ 100 = `r personas_mascota1` personas

### **Solución Parte B:**
Comparando las cantidades:
- `r nombremascota1`: `r personas_mascota1` personas
- `r nombremascota2`: `r personas_mascota2` personas  
- `r nombremascota3`: `r personas_mascota3` personas

La mascota más popular es `r nombre_mas_popular` con `r cantidad_mas_popular` personas.
Diferencia con la segunda: `r diferencia_primera_segunda` personas.

### **Solución Parte C:**
Con `r encuestados_doble` encuestados (el doble), manteniendo las proporciones:
- `r nombre_mas_popular` tendría: `r cantidad_mas_popular` × 2 = `r proyeccion_mas_popular` personas

### **Solución Parte D:**
Los porcentajes muestran proporciones relativas, mientras que las cantidades absolutas revelan el impacto real. Ambos son necesarios para tomar decisiones informadas sobre recursos y planificación.

```{r test_diversidad, echo=FALSE, results="hide"}
# Test de diversidad de versiones
test_that("Diversidad de versiones", {
  versiones <- replicate(500, {
    selmascota_test <- sample(mascotas, 3)
    enkuestados_test <- sample(numeros_posibles, 1)
    porcentajes_test <- generar_porcentajes_complejos()
    digest(paste(selmascota_test, enkuestados_test, porcentajes_test, collapse = "-"))
  })
  
  expect_true(length(unique(versiones)) >= 400,
              info = paste("Solo", length(unique(versiones)), "versiones únicas de 500"))
})
```

**Meta-information**
================
extype: cloze
exsolution: `r mchoice2string(c(pos_correcta_a == 1, pos_correcta_a == 2, pos_correcta_a == 3, pos_correcta_a == 4))`|`r mchoice2string(c(pos_correcta_b == 1, pos_correcta_b == 2, pos_correcta_b == 3, pos_correcta_b == 4))`|`r mchoice2string(c(pos_correcta_c == 1, pos_correcta_c == 2, pos_correcta_c == 3, pos_correcta_c == 4))`|nil
exclozetype: schoice|schoice|schoice|essay
exname: Adopción Mascotas Secuencial
exsection: Estadística|Gráficos|Análisis Secuencial
