---
title: "Pregunta de Justificación Múltiple - ICFES"
output: 
  exams::exams2moodle:
    name: "justificacion_multiple"
---

```{r data_generation, echo=FALSE, results="hide"}
library(exams)

# GENERACIÓN DE PROBLEMA COMPLEJO
set.seed(sample(1:1000, 1))

# Datos del problema: Función cuadrática
a <- sample(c(-2, -1, 1, 2), 1)
b <- sample(-6:6, 1)
c <- sample(-10:10, 1)

# Vértice de la parábola
vertice_x <- -b / (2 * a)
vertice_y <- a * vertice_x^2 + b * vertice_x + c

# Discriminante
discriminante <- b^2 - 4*a*c

# Características de la función
concavidad <- ifelse(a > 0, "hacia arriba", "hacia abajo")
tiene_raices_reales <- discriminante >= 0
num_raices <- ifelse(discriminante > 0, 2, ifelse(discriminante == 0, 1, 0))

# AFIRMACIONES PARA EVALUAR (Verdadero/Falso con justificación)
afirmaciones <- list(
  list(
    afirmacion = paste0("La función f(x) = ", a, "x² + ", b, "x + ", c, " tiene concavidad ", concavidad),
    es_verdadera = TRUE,
    justificacion_correcta = paste0("Porque a = ", a, ifelse(a > 0, " > 0", " < 0")),
    justificaciones_incorrectas = c(
      paste0("Porque b = ", b),
      paste0("Porque c = ", c),
      "Porque el discriminante es positivo"
    )
  ),
  list(
    afirmacion = paste0("El vértice está en (", round(vertice_x, 2), ", ", round(vertice_y, 2), ")"),
    es_verdadera = TRUE,
    justificacion_correcta = paste0("Porque x = -b/(2a) = -", b, "/(2×", a, ") = ", round(vertice_x, 2)),
    justificaciones_incorrectas = c(
      "Porque es el punto de corte con el eje y",
      "Porque es donde la derivada es máxima",
      "Porque es el punto medio entre las raíces"
    )
  ),
  list(
    afirmacion = paste0("La función tiene ", num_raices, " raíces reales"),
    es_verdadera = TRUE,
    justificacion_correcta = paste0("Porque Δ = b² - 4ac = ", discriminante, ifelse(discriminante > 0, " > 0", ifelse(discriminante == 0, " = 0", " < 0"))),
    justificaciones_incorrectas = c(
      "Porque la función es cuadrática",
      "Porque el coeficiente principal es diferente de cero",
      "Porque el vértice está en el primer cuadrante"
    )
  )
)

# Seleccionar 2 afirmaciones aleatoriamente
afirmaciones_seleccionadas <- sample(afirmaciones, 2)
```

## Análisis de Función Cuadrática con Justificación

**CONTEXTO:** Analiza la función cuadrática f(x) = `r a`x² + `r b`x + `r c`

Para cada afirmación, debes:
1. Determinar si es VERDADERA o FALSA
2. Seleccionar la justificación CORRECTA

---

### **AFIRMACIÓN 1:**
**"`r afirmaciones_seleccionadas[[1]]$afirmacion`"**

**1A.** Esta afirmación es:
```{r afirmacion1_vf, echo=FALSE, results="asis"}
opciones_vf1 <- c("VERDADERA", "FALSA")
if(!afirmaciones_seleccionadas[[1]]$es_verdadera) {
  opciones_vf1 <- rev(opciones_vf1)
}
opciones_vf1 <- sample(opciones_vf1)
pos_correcta_vf1 <- which(opciones_vf1 == ifelse(afirmaciones_seleccionadas[[1]]$es_verdadera, "VERDADERA", "FALSA"))

for(i in 1:2) {
  cat(LETTERS[i], ") ", opciones_vf1[i], "\n", sep="")
}
```

**1B.** La justificación correcta es:
```{r justificacion1, echo=FALSE, results="asis"}
todas_justificaciones1 <- c(
  afirmaciones_seleccionadas[[1]]$justificacion_correcta,
  afirmaciones_seleccionadas[[1]]$justificaciones_incorrectas
)
todas_justificaciones1 <- sample(todas_justificaciones1)
pos_correcta_just1 <- which(todas_justificaciones1 == afirmaciones_seleccionadas[[1]]$justificacion_correcta)

for(i in 1:4) {
  cat(LETTERS[i], ") ", todas_justificaciones1[i], "\n", sep="")
}
```

---

### **AFIRMACIÓN 2:**
**"`r afirmaciones_seleccionadas[[2]]$afirmacion`"**

**2A.** Esta afirmación es:
```{r afirmacion2_vf, echo=FALSE, results="asis"}
opciones_vf2 <- c("VERDADERA", "FALSA")
if(!afirmaciones_seleccionadas[[2]]$es_verdadera) {
  opciones_vf2 <- rev(opciones_vf2)
}
opciones_vf2 <- sample(opciones_vf2)
pos_correcta_vf2 <- which(opciones_vf2 == ifelse(afirmaciones_seleccionadas[[2]]$es_verdadera, "VERDADERA", "FALSA"))

for(i in 1:2) {
  cat(LETTERS[i], ") ", opciones_vf2[i], "\n", sep="")
}
```

**2B.** La justificación correcta es:
```{r justificacion2, echo=FALSE, results="asis"}
todas_justificaciones2 <- c(
  afirmaciones_seleccionadas[[2]]$justificacion_correcta,
  afirmaciones_seleccionadas[[2]]$justificaciones_incorrectas
)
todas_justificaciones2 <- sample(todas_justificaciones2)
pos_correcta_just2 <- which(todas_justificaciones2 == afirmaciones_seleccionadas[[2]]$justificacion_correcta)

for(i in 1:4) {
  cat(LETTERS[i], ") ", todas_justificaciones2[i], "\n", sep="")
}
```

---

### **REFLEXIÓN FINAL:**
**3.** Explica en tus propias palabras por qué es importante justificar las respuestas en matemáticas:
{1:ESSAY}

**Meta-information**
================
extype: cloze
exsolution: `r mchoice2string(c(pos_correcta_vf1 == 1, pos_correcta_vf1 == 2))`|`r mchoice2string(c(pos_correcta_just1 == 1, pos_correcta_just1 == 2, pos_correcta_just1 == 3, pos_correcta_just1 == 4))`|`r mchoice2string(c(pos_correcta_vf2 == 1, pos_correcta_vf2 == 2))`|`r mchoice2string(c(pos_correcta_just2 == 1, pos_correcta_just2 == 2, pos_correcta_just2 == 3, pos_correcta_just2 == 4))`|nil
exclozetype: schoice|schoice|schoice|schoice|essay
exname: Justificación Múltiple
