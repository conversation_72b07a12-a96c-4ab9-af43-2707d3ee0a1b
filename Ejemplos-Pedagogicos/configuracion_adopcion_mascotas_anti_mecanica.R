# CONFIGURACIÓN ESPECÍFICA ANTI-MECÁNICA
# Pregunta: Adopción de Mascotas Secuencial
# Basada en: I_1796473-Opc-A2.Rmd (transformada)

library(exams)

# =============================================================================
# CONFIGURACIÓN PRINCIPAL ANTI-MECÁNICA
# =============================================================================

configurar_adopcion_mascotas_anti_mecanica <- function() {
  
  cat("🎯 CONFIGURANDO EXAMEN ANTI-MECÁNICO: Adopción de Mascotas\n")
  cat("=" %R% 60, "\n")
  
  # Archivo de la pregunta transformada
  archivo_examen <- "adopcion_mascotas_secuencial_anti_mecanica.Rmd"
  
  # Verificar que el archivo existe
  if(!file.exists(archivo_examen)) {
    stop("❌ Error: No se encuentra el archivo ", archivo_examen)
  }
  
  # Configuración de semilla para reproducibilidad controlada
  set.seed(sample(1:100000, 1))
  semilla_usada <- .Random.seed[1]
  
  cat("🎲 Semilla utilizada:", semilla_usada, "\n")
  cat("📁 Archivo fuente:", archivo_examen, "\n\n")
  
  # ==========================================================================
  # CONFIGURACIÓN 1: EXAMEN SECUENCIAL ESTRICTO
  # ==========================================================================
  
  cat("🔒 GENERANDO: Configuración Secuencial Estricta\n")
  
  exams2moodle(
    archivo_examen,
    n = 3,  # Pocas versiones para control de calidad
    name = "adopcion_mascotas_secuencial_estricto",
    encoding = "UTF-8",
    dir = "salida",
    
    # CONFIGURACIONES ANTI-MECÁNICAS ESTRICTAS
    cloze = list(
      eval = list(
        partial = FALSE,        # REQUIERE todas las partes correctas
        rule = "none"          # Sin puntuación parcial entre secciones
      )
    ),
    
    # CONFIGURACIONES DE EXAMEN
    exextra = list(
      # TIEMPO Y INTENTOS
      "quiz_attempts" = "1",           # SOLO UN INTENTO
      "quiz_timelimit" = "2700",       # 45 minutos (tiempo generoso para reflexión)
      "quiz_grademethod" = "highest",  # Calificación más alta
      
      # NAVEGACIÓN SECUENCIAL OBLIGATORIA
      "quiz_navmethod" = "sequential", # NO puede saltar preguntas
      "quiz_canredoquestions" = "0",   # NO puede volver atrás
      
      # SIN RETROALIMENTACIÓN DURANTE EL EXAMEN
      "quiz_review_attempt" = "0",     # NO revisar durante intento
      "quiz_review_correctness" = "0", # NO mostrar corrección inmediata
      "quiz_review_marks" = "0",       # NO mostrar puntuación inmediata
      "quiz_review_feedback" = "0",    # NO mostrar retroalimentación inmediata
      
      # RETROALIMENTACIÓN SOLO AL FINAL
      "quiz_review_afterclose_attempt" = "1",     # SÍ revisar después de cerrar
      "quiz_review_afterclose_correctness" = "1", # SÍ mostrar corrección al final
      "quiz_review_afterclose_marks" = "1",       # SÍ mostrar puntuación al final
      "quiz_review_afterclose_feedback" = "1",    # SÍ mostrar retroalimentación al final
      
      # CONFIGURACIONES DE MONITOREO
      "quiz_log_responses" = "1",      # Registrar todas las respuestas
      "quiz_log_timing" = "1",         # Registrar tiempos (detectar adivinación)
      "quiz_shufflequestions" = "0",   # NO mezclar preguntas (mantener secuencia)
      "quiz_shuffleanswers" = "1"      # SÍ mezclar respuestas dentro de cada pregunta
    ),
    
    # CONFIGURACIONES ADICIONALES
    converter = "pandoc-mathjax",  # Mejor renderizado matemático
    base64 = FALSE                 # Imágenes como archivos separados
  )
  
  cat("✅ Configuración Secuencial Estricta generada\n\n")
  
  # ==========================================================================
  # CONFIGURACIÓN 2: EXAMEN CON ANÁLISIS DE PATRONES
  # ==========================================================================
  
  cat("📊 GENERANDO: Configuración con Análisis de Patrones\n")
  
  exams2moodle(
    archivo_examen,
    n = 8,  # Más versiones para análisis estadístico
    name = "adopcion_mascotas_analisis_patrones",
    encoding = "UTF-8",
    dir = "salida",
    
    # CONFIGURACIONES PARA DETECTAR RESOLUCIÓN MECÁNICA
    cloze = list(
      eval = list(
        partial = TRUE,         # Permitir puntuación parcial para análisis
        rule = "none"          # Evaluación estándar
      )
    ),
    
    # CONFIGURACIONES DE MONITOREO AVANZADO
    exextra = list(
      # TIEMPO EXTENDIDO PARA ANÁLISIS PROFUNDO
      "quiz_attempts" = "2",           # Máximo 2 intentos
      "quiz_timelimit" = "3600",       # 60 minutos
      "quiz_delay1" = "900",           # 15 min espera entre intentos
      "quiz_grademethod" = "average",  # Promedio de intentos
      
      # NAVEGACIÓN LIBRE PARA ANÁLISIS
      "quiz_navmethod" = "free",       # Navegación libre
      "quiz_canredoquestions" = "1",   # Puede volver a preguntas
      
      # RETROALIMENTACIÓN DIFERIDA
      "quiz_review_attempt" = "0",     # NO durante el intento
      "quiz_review_correctness" = "1", # SÍ mostrar corrección AL FINAL
      "quiz_review_marks" = "1",       # SÍ mostrar puntuación AL FINAL
      "quiz_review_feedback" = "1",    # SÍ mostrar retroalimentación AL FINAL
      
      # ANÁLISIS DETALLADO DE COMPORTAMIENTO
      "quiz_log_responses" = "1",      # Registrar respuestas
      "quiz_log_timing" = "1",         # Registrar tiempos detallados
      "quiz_log_navigation" = "1",     # Registrar navegación
      "quiz_shufflequestions" = "1",   # Mezclar para análisis
      "quiz_shuffleanswers" = "1"      # Mezclar respuestas
    )
  )
  
  cat("✅ Configuración de Análisis de Patrones generada\n\n")
  
  # ==========================================================================
  # CONFIGURACIÓN 3: EXAMEN EDUCATIVO CON RETROALIMENTACIÓN RICA
  # ==========================================================================
  
  cat("🎓 GENERANDO: Configuración Educativa con Retroalimentación Rica\n")
  
  exams2moodle(
    archivo_examen,
    n = 5,
    name = "adopcion_mascotas_educativo",
    encoding = "UTF-8",
    dir = "salida",
    
    # CONFIGURACIONES EDUCATIVAS
    cloze = list(
      eval = list(
        partial = TRUE,         # Puntuación parcial educativa
        rule = "none"
      )
    ),
    
    # CONFIGURACIONES PARA APRENDIZAJE
    exextra = list(
      # MÚLTIPLES OPORTUNIDADES DE APRENDIZAJE
      "quiz_attempts" = "3",           # Hasta 3 intentos
      "quiz_timelimit" = "0",          # Sin límite de tiempo
      "quiz_delay1" = "1800",          # 30 min entre intentos
      "quiz_delay2" = "3600",          # 60 min entre 2do y 3er intento
      "quiz_grademethod" = "highest",  # Mejor calificación
      
      # NAVEGACIÓN EDUCATIVA
      "quiz_navmethod" = "free",       # Navegación libre
      "quiz_canredoquestions" = "1",   # Puede revisar preguntas
      
      # RETROALIMENTACIÓN EDUCATIVA INMEDIATA
      "quiz_review_attempt" = "1",     # SÍ revisar durante intento
      "quiz_review_correctness" = "1", # SÍ mostrar corrección
      "quiz_review_marks" = "0",       # NO mostrar puntuación (evitar ansiedad)
      "quiz_review_feedback" = "1",    # SÍ mostrar retroalimentación educativa
      "quiz_review_generalfeedback" = "1", # SÍ mostrar retroalimentación general
      
      # CONFIGURACIONES DE APOYO
      "quiz_log_responses" = "1",      # Para seguimiento del progreso
      "quiz_shufflequestions" = "0",   # Mantener orden lógico
      "quiz_shuffleanswers" = "1"      # Mezclar respuestas
    )
  )
  
  cat("✅ Configuración Educativa generada\n\n")
  
  # ==========================================================================
  # RESUMEN Y RECOMENDACIONES
  # ==========================================================================
  
  cat("📋 RESUMEN DE CONFIGURACIONES GENERADAS:\n")
  cat("=" %R% 50, "\n")
  cat("1. 🔒 adopcion_mascotas_secuencial_estricto.xml\n")
  cat("   - Un solo intento, navegación secuencial\n")
  cat("   - Sin retroalimentación inmediata\n")
  cat("   - Ideal para: Evaluación formal anti-mecánica\n\n")
  
  cat("2. 📊 adopcion_mascotas_analisis_patrones.xml\n")
  cat("   - Dos intentos, monitoreo avanzado\n")
  cat("   - Retroalimentación diferida\n")
  cat("   - Ideal para: Análisis de comportamiento estudiantil\n\n")
  
  cat("3. 🎓 adopcion_mascotas_educativo.xml\n")
  cat("   - Tres intentos, retroalimentación rica\n")
  cat("   - Sin límite de tiempo\n")
  cat("   - Ideal para: Práctica y aprendizaje\n\n")
  
  cat("🎯 RECOMENDACIONES DE USO:\n")
  cat("- Usar configuración 1 para exámenes oficiales\n")
  cat("- Usar configuración 2 para investigación pedagógica\n")
  cat("- Usar configuración 3 para práctica y entrenamiento\n\n")
  
  cat("📁 Archivos generados en directorio: salida/\n")
  cat("🔧 Importar en Moodle como: Formato XML de Moodle\n\n")
  
  cat("✅ PROCESO COMPLETADO EXITOSAMENTE\n")
  
  return(list(
    semilla = semilla_usada,
    archivo_fuente = archivo_examen,
    configuraciones = c("secuencial_estricto", "analisis_patrones", "educativo"),
    directorio_salida = "salida"
  ))
}

# =============================================================================
# FUNCIÓN DE ANÁLISIS POST-APLICACIÓN
# =============================================================================

analizar_resultados_adopcion_mascotas <- function(archivo_resultados_moodle) {
  
  cat("📊 ANALIZANDO RESULTADOS DE ADOPCIÓN DE MASCOTAS\n")
  cat("=" %R% 50, "\n")
  
  # Esta función se implementaría para analizar los resultados exportados de Moodle
  # Métricas clave a analizar:
  
  metricas_clave <- list(
    "tiempo_promedio_parte_a" = "¿Tiempo promedio en Parte A? (esperado: 3-5 min)",
    "tiempo_promedio_parte_b" = "¿Tiempo promedio en Parte B? (esperado: 4-6 min)",
    "tiempo_promedio_parte_c" = "¿Tiempo promedio en Parte C? (esperado: 3-5 min)",
    "tiempo_promedio_parte_d" = "¿Tiempo promedio en Parte D? (esperado: 5-8 min)",
    "correlacion_partes" = "¿Correlación entre partes? (esperado: >0.6)",
    "distribucion_respuestas" = "¿Distribución uniforme? (señal de adivinación)",
    "patrones_navegacion" = "¿Patrones de navegación secuencial?",
    "calidad_ensayos" = "¿Calidad de respuestas en Parte D?"
  )
  
  cat("🔍 MÉTRICAS CLAVE A REVISAR:\n")
  for(i in 1:length(metricas_clave)) {
    cat(sprintf("%d. %s\n", i, metricas_clave[[i]]))
  }
  
  cat("\n⚠️  SEÑALES DE ALERTA:\n")
  cat("- Tiempo < 2 min por parte = Posible adivinación\n")
  cat("- Distribución uniforme = Resolución mecánica\n")
  cat("- Baja correlación entre partes = Falta de comprensión secuencial\n")
  cat("- Respuestas vacías en Parte D = Evitación de análisis\n\n")
  
  cat("📈 ACCIONES RECOMENDADAS SEGÚN RESULTADOS:\n")
  cat("- Si tiempo muy corto: Aumentar complejidad\n")
  cat("- Si distribución uniforme: Mejorar distractores\n")
  cat("- Si baja correlación: Reforzar dependencias secuenciales\n")
  cat("- Si ensayos pobres: Proporcionar más ejemplos\n\n")
}

# =============================================================================
# FUNCIÓN PRINCIPAL DE EJECUCIÓN
# =============================================================================

ejecutar_configuracion_completa <- function() {
  
  cat("🚀 INICIANDO CONFIGURACIÓN COMPLETA ANTI-MECÁNICA\n")
  cat("Pregunta: Adopción de Mascotas (Transformación Pedagógica)\n")
  cat("Fecha:", Sys.time(), "\n\n")
  
  # Crear directorio de salida si no existe
  if(!dir.exists("salida")) {
    dir.create("salida")
    cat("📁 Directorio 'salida' creado\n")
  }
  
  # Ejecutar configuración principal
  resultado <- configurar_adopcion_mascotas_anti_mecanica()
  
  cat("\n🎉 CONFIGURACIÓN COMPLETA FINALIZADA\n")
  cat("Revise los archivos XML generados en el directorio 'salida'\n")
  cat("Importe en Moodle y monitoree las métricas recomendadas\n\n")
  
  return(resultado)
}

# =============================================================================
# EJEMPLO DE USO
# =============================================================================

# Para ejecutar la configuración completa:
# resultado <- ejecutar_configuracion_completa()

# Para analizar resultados después de aplicar:
# analizar_resultados_adopcion_mascotas("resultados_moodle.csv")
