---
title: "Geometría Simple - VERSIÓN GARANTIZADA"
---

```{r data_generation, echo=FALSE, results="hide"}
library(exams)

# Datos fijos para evitar problemas
largo <- 12
ancho <- 8
area_rectangulo <- largo * ancho  # 96

# Círculo inscrito
radio_circulo <- ancho / 2  # 4
area_circulo <- pi * radio_circulo^2  # 50.27

# Área sombreada
area_sombreada <- area_rectangulo - area_circulo  # 45.73

# Opciones fijas para Parte A
opciones_a <- c(
  "96 m²",      # Correcto
  "20 m²",      # Perímetro
  "40 m²",      # Perímetro completo
  "48 m²"       # División incorrecta
)

# Opciones fijas para Parte B  
opciones_b <- c(
  "50.27 m²",   # Correcto
  "201.06 m²",  # Error: usar ancho como radio
  "12.57 m²",   # Error: radio incorrecto
  "25.13 m²"    # Error: perímetro
)

# Opciones fijas para Parte C
opciones_c <- c(
  "45.73 m²",   # Correcto
  "146.27 m²",  # Error: suma
  "4.54 m²",    # Error: orden invertido (valor absoluto)
  "48.00 m²"    # Error: ignorar círculo
)

# Mezclar opciones
set.seed(123)  # Para reproducibilidad
opciones_a_mezcladas <- sample(opciones_a)
opciones_b_mezcladas <- sample(opciones_b)
opciones_c_mezcladas <- sample(opciones_c)

# Encontrar posiciones correctas
pos_a <- which(opciones_a_mezcladas == "96 m²")
pos_b <- which(opciones_b_mezcladas == "50.27 m²")
pos_c <- which(opciones_c_mezcladas == "45.73 m²")
```

## Problema de Geometría - Parque con Fuente

**CONTEXTO:** En un parque se diseña una zona rectangular de 12 metros de largo por 8 metros de ancho, con una fuente circular inscrita.

### **PARTE A: Área del Rectángulo**

¿Cuál es el área total de la zona rectangular?

```{r parte_a, echo=FALSE, results="asis"}
for(i in 1:4) {
  cat(LETTERS[i], ") ", opciones_a_mezcladas[i], "\n", sep="")
}
```

### **PARTE B: Área de la Fuente**

Si la fuente circular es el círculo más grande que cabe inscrito en el rectángulo, ¿cuál es su área?

```{r parte_b, echo=FALSE, results="asis"}
for(i in 1:4) {
  cat(LETTERS[i], ") ", opciones_b_mezcladas[i], "\n", sep="")
}
```

### **PARTE C: Área para Caminar**

¿Cuál es el área disponible para caminar (área sombreada)?

```{r parte_c, echo=FALSE, results="asis"}
for(i in 1:4) {
  cat(LETTERS[i], ") ", opciones_c_mezcladas[i], "\n", sep="")
}
```

Meta-information
================
extype: cloze
exsolution: `r pos_a`|`r pos_b`|`r pos_c`
exclozetype: schoice|schoice|schoice
exname: Geometria Simple
