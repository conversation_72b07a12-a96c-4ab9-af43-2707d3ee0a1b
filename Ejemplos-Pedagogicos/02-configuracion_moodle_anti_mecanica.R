# CONFIGURACIÓN MOODLE ANTI-MECÁNICA PARA ICFES MATEMÁTICAS
# Estrategias específicas para fomentar razonamiento analítico

library(exams)

# =============================================================================
# CONFIGURACIÓN 1: EXAMEN SECUENCIAL SIN RETROALIMENTACIÓN INMEDIATA
# =============================================================================

configuracion_secuencial <- function(archivo_examen, nombre_archivo) {
  
  set.seed(sample(1:10000, 1))
  
  exams2moodle(
    archivo_examen,
    n = 5,  # Múltiples versiones
    name = paste0(nombre_archivo, "_secuencial"),
    encoding = "UTF-8",
    dir = "salida",
    
    # CONFIGURACIONES ANTI-MECÁNICAS
    mchoice = list(
      shuffle = TRUE,           # Mezclar opciones
      answernumbering = "ABCD", # Numeración estándar
      eval = list(
        partial = FALSE,        # NO puntuación parcial (todo o nada)
        negative = TRUE,        # Penalización por respuestas incorrectas
        rule = "false2"         # Regla estricta: -50% por respuesta incorrecta
      )
    ),
    
    # CONFIGURACIONES ESPECÍFICAS PARA CLOZE
    cloze = list(
      eval = list(
        partial = FALSE,        # Requiere todas las partes correctas
        rule = "none"          # Sin puntuación parcial entre secciones
      )
    ),
    
    # CONFIGURACIONES ADICIONALES
    converter = "pandoc-mathjax",  # Mejor renderizado matemático
    base64 = FALSE,               # Imágenes como archivos separados
    
    # METADATOS PARA MOODLE
    exextra = list(
      # Configuraciones de tiempo y intentos
      "quiz_attempts" = "1",           # Solo UN intento
      "quiz_timelimit" = "3600",       # 60 minutos máximo
      "quiz_grademethod" = "highest",  # Calificación más alta
      "quiz_shufflequestions" = "1",   # Mezclar preguntas
      "quiz_shuffleanswers" = "1",     # Mezclar respuestas
      
      # Configuraciones de retroalimentación
      "quiz_review_attempt" = "0",     # NO revisar durante el intento
      "quiz_review_correctness" = "0", # NO mostrar corrección inmediata
      "quiz_review_marks" = "0",       # NO mostrar puntuación inmediata
      "quiz_review_feedback" = "0"     # NO mostrar retroalimentación inmediata
    )
  )
}

# =============================================================================
# CONFIGURACIÓN 2: EXAMEN CON RETROALIMENTACIÓN DIFERIDA INTELIGENTE
# =============================================================================

configuracion_retroalimentacion_diferida <- function(archivo_examen, nombre_archivo) {
  
  set.seed(sample(1:10000, 1))
  
  exams2moodle(
    archivo_examen,
    n = 3,
    name = paste0(nombre_archivo, "_diferida"),
    encoding = "UTF-8",
    dir = "salida",
    
    # CONFIGURACIONES PARA FOMENTAR REFLEXIÓN
    mchoice = list(
      shuffle = TRUE,
      answernumbering = "ABCD",
      eval = list(
        partial = TRUE,         # Permitir puntuación parcial
        negative = FALSE,       # Sin penalización (para fomentar intentos)
        rule = "none"          # Evaluación estándar
      )
    ),
    
    # CONFIGURACIONES DE TIEMPO EXTENDIDO
    exextra = list(
      "quiz_attempts" = "2",           # Máximo 2 intentos
      "quiz_timelimit" = "5400",       # 90 minutos (tiempo extendido)
      "quiz_delay1" = "1800",          # 30 min espera entre intentos
      "quiz_grademethod" = "average",   # Promedio de intentos
      
      # Retroalimentación SOLO al final
      "quiz_review_attempt" = "0",      # NO durante el intento
      "quiz_review_correctness" = "1",  # SÍ mostrar corrección AL FINAL
      "quiz_review_marks" = "1",        # SÍ mostrar puntuación AL FINAL
      "quiz_review_feedback" = "1",     # SÍ mostrar retroalimentación AL FINAL
      "quiz_review_answers" = "1"       # SÍ mostrar respuestas correctas AL FINAL
    )
  )
}

# =============================================================================
# CONFIGURACIÓN 3: EXAMEN ADAPTATIVO CON PREGUNTAS DEPENDIENTES
# =============================================================================

configuracion_adaptativa <- function(lista_ejercicios, nombre_archivo) {
  
  # Lista de ejercicios en orden de dependencia
  # Ejemplo: [geometria_basica.Rmd, geometria_intermedia.Rmd, geometria_avanzada.Rmd]
  
  set.seed(sample(1:10000, 1))
  
  exams2moodle(
    lista_ejercicios,  # Lista ordenada de ejercicios dependientes
    n = 1,             # Una sola versión para mantener secuencia
    name = paste0(nombre_archivo, "_adaptativo"),
    encoding = "UTF-8",
    dir = "salida",
    
    # CONFIGURACIONES PARA SECUENCIA LÓGICA
    mchoice = list(
      shuffle = FALSE,        # NO mezclar (mantener orden lógico)
      answernumbering = "ABCD",
      eval = list(
        partial = FALSE,      # Requiere respuesta completa correcta
        rule = "false4"       # Penalización fuerte por error
      )
    ),
    
    # CONFIGURACIONES ESTRICTAS
    exextra = list(
      "quiz_attempts" = "1",           # Solo UN intento
      "quiz_timelimit" = "7200",       # 2 horas para reflexión profunda
      "quiz_navmethod" = "sequential", # Navegación SECUENCIAL obligatoria
      "quiz_canredoquestions" = "0",   # NO puede volver a preguntas anteriores
      
      # Sin retroalimentación hasta el final
      "quiz_review_attempt" = "0",
      "quiz_review_correctness" = "0",
      "quiz_review_marks" = "0",
      "quiz_review_feedback" = "0"
    )
  )
}

# =============================================================================
# CONFIGURACIÓN 4: EXAMEN CON ANÁLISIS DE PATRONES DE RESPUESTA
# =============================================================================

configuracion_analisis_patrones <- function(archivo_examen, nombre_archivo) {
  
  set.seed(sample(1:10000, 1))
  
  exams2moodle(
    archivo_examen,
    n = 10,  # Muchas versiones para análisis estadístico
    name = paste0(nombre_archivo, "_patrones"),
    encoding = "UTF-8",
    dir = "salida",
    
    # CONFIGURACIONES PARA DETECTAR ADIVINACIÓN
    mchoice = list(
      shuffle = TRUE,
      answernumbering = "ABCD",
      eval = list(
        partial = FALSE,
        negative = TRUE,        # Penalización por adivinación
        rule = "false3"        # -33% por respuesta incorrecta
      )
    ),
    
    # CONFIGURACIONES DE MONITOREO
    exextra = list(
      "quiz_attempts" = "1",
      "quiz_timelimit" = "4500",       # 75 minutos
      "quiz_grademethod" = "highest",
      
      # Configuraciones para análisis posterior
      "quiz_log_responses" = "1",      # Registrar todas las respuestas
      "quiz_log_timing" = "1",         # Registrar tiempos de respuesta
      "quiz_require_password" = "0",   # Sin contraseña para facilitar acceso
      
      # Retroalimentación educativa diferida
      "quiz_review_attempt" = "0",
      "quiz_review_correctness" = "1", # Solo al final
      "quiz_review_marks" = "1",
      "quiz_review_feedback" = "1",
      "quiz_review_generalfeedback" = "1"  # Retroalimentación general educativa
    )
  )
}

# =============================================================================
# FUNCIÓN PRINCIPAL DE CONFIGURACIÓN
# =============================================================================

aplicar_configuracion_anti_mecanica <- function(tipo_configuracion, archivo_examen, nombre_archivo) {
  
  cat("🎯 Aplicando configuración anti-mecánica:", tipo_configuracion, "\n")
  
  switch(tipo_configuracion,
    "secuencial" = configuracion_secuencial(archivo_examen, nombre_archivo),
    "diferida" = configuracion_retroalimentacion_diferida(archivo_examen, nombre_archivo),
    "adaptativa" = configuracion_adaptativa(archivo_examen, nombre_archivo),
    "patrones" = configuracion_analisis_patrones(archivo_examen, nombre_archivo),
    stop("Tipo de configuración no válido. Opciones: 'secuencial', 'diferida', 'adaptativa', 'patrones'")
  )
  
  cat("✅ Configuración aplicada exitosamente\n")
  cat("📁 Archivos generados en directorio 'salida/'\n")
}

# =============================================================================
# EJEMPLOS DE USO
# =============================================================================

# Ejemplo 1: Examen secuencial sin retroalimentación
# aplicar_configuracion_anti_mecanica("secuencial", "mi_ejercicio.Rmd", "examen_icfes")

# Ejemplo 2: Examen con retroalimentación diferida
# aplicar_configuracion_anti_mecanica("diferida", "mi_ejercicio.Rmd", "examen_icfes")

# Ejemplo 3: Examen adaptativo con preguntas dependientes
# lista_ejercicios <- c("parte1.Rmd", "parte2.Rmd", "parte3.Rmd")
# aplicar_configuracion_anti_mecanica("adaptativa", lista_ejercicios, "examen_icfes")

# Ejemplo 4: Examen con análisis de patrones
# aplicar_configuracion_anti_mecanica("patrones", "mi_ejercicio.Rmd", "examen_icfes")
