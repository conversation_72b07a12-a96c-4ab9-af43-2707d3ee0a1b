---
title: "Pregunta Cloze Compleja - Estadística ICFES"
output: 
  exams::exams2moodle:
    name: "estadistica_cloze_compleja"
---

```{r data_generation, echo=FALSE, results="hide"}
library(exams)

# GENERACIÓN DE DATOS COMPLEJOS
set.seed(sample(1:1000, 1))

# Datos de calificaciones de estudiantes
n_estudiantes <- sample(25:35, 1)
calificaciones <- round(rnorm(n_estudiantes, mean = sample(70:85, 1), sd = sample(8:12, 1)), 1)
calificaciones <- pmax(0, pmin(100, calificaciones))  # Limitar entre 0 y 100

# CÁLCULOS ESTADÍSTICOS
media <- round(mean(calificaciones), 2)
mediana <- round(median(calificaciones), 2)
desviacion <- round(sd(calificaciones), 2)
rango <- max(calificaciones) - min(calificaciones)

# CLASIFICACIONES
aprobados <- sum(calificaciones >= 60)
reprobados <- n_estudiantes - aprobados
porcentaje_aprobados <- round((aprobados/n_estudiantes) * 100, 1)

# INTERPRETACIONES PARA MÚLTIPLE CHOICE
interpretaciones <- c(
  "La mayoría de estudiantes tiene un rendimiento promedio",
  "Existe una gran variabilidad en las calificaciones",
  "El grupo muestra un rendimiento homogéneo",
  "Las calificaciones están muy concentradas alrededor de la media"
)

# Determinar interpretación correcta basada en desviación estándar
if(desviacion > 15) {
  interpretacion_correcta <- 2  # Gran variabilidad
} else if(desviacion < 8) {
  interpretacion_correcta <- 4  # Concentradas
} else {
  interpretacion_correcta <- 1  # Promedio
}

# ANÁLISIS VERDADERO/FALSO
afirmaciones_vf <- c(
  paste0("La media (", media, ") es mayor que la mediana (", mediana, ")"),
  paste0("Más del 70% de estudiantes aprobó"),
  paste0("La desviación estándar indica alta dispersión"),
  paste0("El rango de calificaciones es menor a 30 puntos")
)

respuestas_vf <- c(
  media > mediana,
  porcentaje_aprobados > 70,
  desviacion > 12,
  rango < 30
)
```

## Análisis Estadístico Complejo - Calificaciones ICFES

**CONTEXTO:** Un colegio analizó las calificaciones de `r n_estudiantes` estudiantes en la prueba de matemáticas. Los datos muestran: Media = `r media`, Mediana = `r mediana`, Desviación estándar = `r desviacion`.

### **SECCIÓN A: Cálculos Numéricos**

**A1.** ¿Cuál es el porcentaje de estudiantes que aprobó (≥60 puntos)?
{1:NUMERICAL:=`r porcentaje_aprobados`:0.1~%50%`r round(porcentaje_aprobados*1.1, 1)`:0.1#Verificar cálculo de porcentaje}%

**A2.** Si se suman todas las calificaciones, ¿cuál es el total?
{1:NUMERICAL:=`r round(media * n_estudiantes, 1)`:0.5}

### **SECCIÓN B: Interpretación Conceptual**

**B1.** Basándose en la desviación estándar (`r desviacion`), ¿cuál es la interpretación más adecuada?
{1:MULTICHOICE:
~%25%`r interpretaciones[1]`
~%25%`r interpretaciones[2]`
~%25%`r interpretaciones[3]`
~%100%`r interpretaciones[interpretacion_correcta]`
}

### **SECCIÓN C: Análisis Verdadero/Falso**

**C1.** `r afirmaciones_vf[1]`
{1:MULTICHOICE:~%50%Verdadero~%100%`r ifelse(respuestas_vf[1], "Verdadero", "Falso")`}

**C2.** `r afirmaciones_vf[2]`
{1:MULTICHOICE:~%50%Falso~%100%`r ifelse(respuestas_vf[2], "Verdadero", "Falso")`}

### **SECCIÓN D: Síntesis Textual**

**D1.** Escriba UNA palabra que mejor describa la distribución de calificaciones:
{1:SHORTANSWER:=`r ifelse(desviacion > 12, "dispersa", ifelse(desviacion < 8, "concentrada", "normal"))`:=heterogénea:=homogénea:=variable}

### **SECCIÓN E: Justificación Extendida**

**E1.** Explique en máximo 100 palabras por qué la media y mediana son diferentes y qué indica esto sobre la distribución:
{1:ESSAY}

**Meta-information**
================
extype: cloze
exsolution: `r porcentaje_aprobados`|`r round(media * n_estudiantes, 1)`|`r sprintf("%04d", as.numeric(intToBits(interpretacion_correcta))[1:4])`|`r ifelse(respuestas_vf[1], "10", "01")`|`r ifelse(respuestas_vf[2], "10", "01")`|`r ifelse(desviacion > 12, "dispersa", ifelse(desviacion < 8, "concentrada", "normal"))`|nil
exclozetype: num|num|mchoice|mchoice|mchoice|string|essay
extol: 0.1|0.5|0|0|0|0|0
exname: Estadística Cloze Compleja
