---
title: "Pregunta Secuencial - Geometría ICFES"
output: 
  exams::exams2moodle:
    name: "geometria_secuencial"
---

```{r data_generation, echo=FALSE, results="hide"}
library(exams)

# GENERACIÓN DE DATOS DEPENDIENTES
# Paso 1: Dimensiones del rectángulo
largo <- sample(8:15, 1)
ancho <- sample(5:9, 1)
area_rectangulo <- largo * ancho

# Paso 2: Radio del círculo inscrito (depende del rectángulo)
# El círculo inscrito en un rectángulo tiene diámetro = lado menor
radio_circulo <- ancho / 2
area_circulo <- pi * radio_circulo^2

# Paso 3: Área sombreada (depende de los cálculos anteriores)
area_sombreada <- area_rectangulo - area_circulo

# DISTRACTORES SECUENCIALES INTELIGENTES
# Para Paso 1: Errores comunes en área
dist1_1 <- largo + ancho  # Perímetro en lugar de área
dist1_2 <- 2 * (largo + ancho)  # Perímetro completo
dist1_3 <- largo * ancho / 2  # División incorrecta

# Para Paso 2: Errores en círculo inscrito
dist2_1 <- pi * ancho^2  # Usar ancho como radio
dist2_2 <- pi * (ancho/4)^2  # Radio incorrecto
dist2_3 <- 2 * pi * radio_circulo  # Perímetro en lugar de área

# Para Paso 3: Errores en área sombreada
dist3_1 <- area_rectangulo + area_circulo  # Suma en lugar de resta
dist3_2 <- area_circulo - area_rectangulo  # Orden invertido
dist3_3 <- area_rectangulo / 2  # Ignorar el círculo
```

## Problema Secuencial de Geometría

**CONTEXTO:** En un parque se diseña una zona rectangular de `r largo` metros de largo por `r ancho` metros de ancho, con una fuente circular inscrita (el círculo más grande que cabe dentro del rectángulo).

### **PARTE A: Análisis del Área Rectangular**

¿Cuál es el área total de la zona rectangular?

```{r parte_a, echo=FALSE, results="asis"}
opciones_a <- c(
  paste0(area_rectangulo, " m²"),
  paste0(dist1_1, " m²"),
  paste0(dist1_2, " m²"),
  paste0(dist1_3, " m²")
)
opciones_a <- sample(opciones_a)
pos_correcta_a <- which(opciones_a == paste0(area_rectangulo, " m²"))

for(i in 1:4) {
  cat(LETTERS[i], ") ", opciones_a[i], "\n", sep="")
}
```

### **PARTE B: Análisis de la Fuente Circular**

**IMPORTANTE:** *Esta pregunta requiere la respuesta correcta de la Parte A*

Si el área rectangular es `r area_rectangulo` m², y sabiendo que la fuente circular es el círculo más grande que cabe inscrito en el rectángulo, ¿cuál es el área de la fuente?

```{r parte_b, echo=FALSE, results="asis"}
opciones_b <- c(
  paste0(round(area_circulo, 2), " m²"),
  paste0(round(dist2_1, 2), " m²"),
  paste0(round(dist2_2, 2), " m²"),
  paste0(round(dist2_3, 2), " m²")
)
opciones_b <- sample(opciones_b)
pos_correcta_b <- which(opciones_b == paste0(round(area_circulo, 2), " m²"))

for(i in 1:4) {
  cat(LETTERS[i], ") ", opciones_b[i], "\n", sep="")
}
```

### **PARTE C: Síntesis y Aplicación**

**IMPORTANTE:** *Esta pregunta requiere las respuestas correctas de las Partes A y B*

Si el área rectangular es `r area_rectangulo` m² y el área de la fuente es `r round(area_circulo, 2)` m², ¿cuál es el área disponible para caminar (área sombreada)?

```{r parte_c, echo=FALSE, results="asis"}
opciones_c <- c(
  paste0(round(area_sombreada, 2), " m²"),
  paste0(round(dist3_1, 2), " m²"),
  paste0(round(abs(dist3_2), 2), " m²"),
  paste0(round(dist3_3, 2), " m²")
)
opciones_c <- sample(opciones_c)
pos_correcta_c <- which(opciones_c == paste0(round(area_sombreada, 2), " m²"))

for(i in 1:4) {
  cat(LETTERS[i], ") ", opciones_c[i], "\n", sep="")
}
```

**Meta-information**
================
extype: cloze
exsolution: `r mchoice2string(c(pos_correcta_a == 1, pos_correcta_a == 2, pos_correcta_a == 3, pos_correcta_a == 4))`|`r mchoice2string(c(pos_correcta_b == 1, pos_correcta_b == 2, pos_correcta_b == 3, pos_correcta_b == 4))`|`r mchoice2string(c(pos_correcta_c == 1, pos_correcta_c == 2, pos_correcta_c == 3, pos_correcta_c == 4))`
exclozetype: schoice|schoice|schoice
exname: Geometría Secuencial
