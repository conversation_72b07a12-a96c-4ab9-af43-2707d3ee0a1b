# =============================================================================
# SCRIPT SIMPLE PARA GENERAR EXAMEN MOODLE
# Solución rápida al error "el argumento 'file' está ausente"
# =============================================================================

# ⚠️  IMPORTANTE: NO uses el botón "Knit" de RStudio
# ✅ EJECUTA este código en la CONSOLA de R

# =============================================================================
# PASO 1: CARGAR LIBRERÍAS
# =============================================================================

# Instalar exams si no está instalado
if(!"exams" %in% rownames(installed.packages())) {
  install.packages("exams")
}

library(exams)

# =============================================================================
# PASO 2: CONFIGURAR DIRECTORIO
# =============================================================================

# Verificar directorio actual
cat("📁 Directorio actual:", getwd(), "\n")

# Cambiar a directorio de ejemplos si existe
if(dir.exists("Ejemplos-Pedagogicos")) {
  setwd("Ejemplos-Pedagogicos")
  cat("✅ Cambiado a: Ejemplos-Pedagogicos\n")
} else {
  cat("⚠️  Directorio 'Ejemplos-Pedagogicos' no encontrado\n")
  cat("   Asegúrate de estar en el directorio raíz del proyecto\n")
}

# =============================================================================
# PASO 3: GENERAR EXAMEN DE EJEMPLO
# =============================================================================

# Archivo de ejemplo (ajusta según el archivo que quieras procesar)
archivo_ejemplo <- "03-pregunta_secuencial_geometria.Rmd"

# Verificar que el archivo existe
if(file.exists(archivo_ejemplo)) {
  
  cat("🎯 Generando examen desde:", archivo_ejemplo, "\n")
  
  # Crear directorio de salida
  if(!dir.exists("salida")) {
    dir.create("salida")
  }
  
  # GENERAR EXAMEN MOODLE
  exams2moodle(
    archivo_ejemplo,          # Archivo .Rmd
    n = 3,                   # Número de versiones
    name = "geometria_ejemplo", # Nombre base
    encoding = "UTF-8",      # Codificación
    dir = "salida"          # Directorio de salida
  )
  
  cat("✅ EXAMEN GENERADO EXITOSAMENTE\n")
  cat("📁 Revisa el directorio: Ejemplos-Pedagogicos/salida/\n")
  cat("📄 Busca archivos .xml para importar en Moodle\n")
  
} else {
  cat("❌ Archivo no encontrado:", archivo_ejemplo, "\n")
  cat("📋 Archivos .Rmd disponibles:\n")
  archivos_disponibles <- list.files(pattern = "\\.Rmd$")
  for(i in 1:length(archivos_disponibles)) {
    cat("  ", i, ".", archivos_disponibles[i], "\n")
  }
}

# =============================================================================
# ALTERNATIVAS PARA OTROS ARCHIVOS
# =============================================================================

cat("\n🔄 PARA GENERAR OTROS ARCHIVOS, USA:\n")
cat("=" %R% 40, "\n")

# Ejemplo 1: Pregunta cloze compleja
cat("# Estadística cloze:\n")
cat('exams2moodle("04-pregunta_cloze_estadistica_compleja.Rmd", n=2, name="estadistica", dir="salida")\n\n')

# Ejemplo 2: Pregunta de justificación
cat("# Justificación múltiple:\n")
cat('exams2moodle("05-pregunta_justificacion_multiple.Rmd", n=2, name="justificacion", dir="salida")\n\n')

# Ejemplo 3: Caso práctico adopción mascotas
cat("# Adopción mascotas (caso práctico):\n")
cat('exams2moodle("06-adopcion_mascotas_secuencial_anti_mecanica.Rmd", n=3, name="adopcion_mascotas", dir="salida")\n\n')

cat("✅ COPIA Y PEGA cualquiera de estos comandos en la consola de R\n")
