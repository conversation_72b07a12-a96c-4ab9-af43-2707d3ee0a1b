# =============================================================================
# CÓDIGO CORREGIDO PARA GENERAR EXAMEN DE GEOMETRÍA
# Solución al error "no exsolution specified"
# =============================================================================

# ⚠️ EJECUTAR EN LA CONSOLA DE R (NO usar botón "Knit")

# =============================================================================
# PASO 1: PREPARACIÓN
# =============================================================================

# Cargar librería
library(exams)

# Verificar y cambiar directorio
cat("📁 Directorio actual:", getwd(), "\n")

if(dir.exists("Ejemplos-Pedagogicos")) {
  setwd("Ejemplos-Pedagogicos")
  cat("✅ Cambiado a: Ejemplos-Pedagogicos\n")
} else {
  cat("⚠️ Asegúrate de estar en el directorio raíz del proyecto\n")
  stop("Directorio incorrecto")
}

# Crear directorio de salida
if(!dir.exists("salida")) {
  dir.create("salida")
  cat("📁 Directorio 'salida' creado\n")
}

# =============================================================================
# PASO 2: VERIFICAR ARCHIVO
# =============================================================================

archivo <- "03-pregunta_secuencial_geometria.Rmd"

if(!file.exists(archivo)) {
  cat("❌ Archivo no encontrado:", archivo, "\n")
  cat("📋 Archivos .Rmd disponibles:\n")
  archivos_disponibles <- list.files(pattern = "\\.Rmd$")
  for(i in 1:length(archivos_disponibles)) {
    cat("  ", i, ".", archivos_disponibles[i], "\n")
  }
  stop("Archivo no encontrado")
}

cat("✅ Archivo encontrado:", archivo, "\n")

# =============================================================================
# PASO 3: DIAGNÓSTICO DEL ARCHIVO
# =============================================================================

cat("🔍 Verificando contenido del archivo...\n")

# Leer contenido
contenido <- readLines(archivo, warn = FALSE)

# Verificar metainformación
tiene_extype <- any(grepl("extype:", contenido))
tiene_exsolution <- any(grepl("exsolution:", contenido))
tiene_exname <- any(grepl("exname:", contenido))

cat("📋 Diagnóstico:\n")
cat("  - extype:", ifelse(tiene_extype, "✅ Presente", "❌ Ausente"), "\n")
cat("  - exsolution:", ifelse(tiene_exsolution, "✅ Presente", "❌ Ausente"), "\n")
cat("  - exname:", ifelse(tiene_exname, "✅ Presente", "❌ Ausente"), "\n")

# =============================================================================
# PASO 4: GENERAR EXAMEN (VERSIÓN ROBUSTA)
# =============================================================================

cat("\n🚀 Generando examen de geometría...\n")

# Intentar generación con manejo de errores
tryCatch({
  
  # MÉTODO 1: Configuración básica
  exams2moodle(
    archivo,
    n = 2,                    # Solo 2 versiones para prueba
    name = "geometria_test",
    encoding = "UTF-8",
    dir = "salida"
  )
  
  cat("✅ EXAMEN GENERADO EXITOSAMENTE (Método 1)\n")
  
}, error = function(e) {
  
  cat("⚠️ Error con Método 1:", as.character(e), "\n")
  cat("🔄 Intentando Método 2...\n")
  
  # MÉTODO 2: Configuración alternativa
  tryCatch({
    
    exams2moodle(
      archivo,
      n = 1,                  # Solo 1 versión
      name = "geometria_simple",
      dir = "salida",
      encoding = "UTF-8",
      
      # Configuración explícita para cloze
      cloze = list(
        eval = list(
          partial = FALSE,
          rule = "none"
        )
      )
    )
    
    cat("✅ EXAMEN GENERADO EXITOSAMENTE (Método 2)\n")
    
  }, error = function(e2) {
    
    cat("❌ Error con Método 2:", as.character(e2), "\n")
    cat("🔄 Intentando Método 3 (HTML para diagnóstico)...\n")
    
    # MÉTODO 3: Generar HTML para diagnóstico
    tryCatch({
      
      exams2html(
        archivo,
        n = 1,
        name = "geometria_diagnostico",
        dir = "salida"
      )
      
      cat("✅ ARCHIVO HTML GENERADO PARA DIAGNÓSTICO\n")
      cat("📄 Revisa: salida/geometria_diagnostico1.html\n")
      
    }, error = function(e3) {
      cat("❌ Error crítico:", as.character(e3), "\n")
      cat("📋 Contenido problemático del archivo:\n")
      
      # Mostrar líneas con metainformación
      lineas_meta <- grep("^ex", contenido, value = TRUE)
      for(linea in lineas_meta) {
        cat("  ", linea, "\n")
      }
    })
  })
})

# =============================================================================
# PASO 5: VERIFICAR RESULTADOS
# =============================================================================

cat("\n📁 VERIFICANDO ARCHIVOS GENERADOS:\n")

# Listar archivos en directorio de salida
archivos_salida <- list.files("salida")

if(length(archivos_salida) > 0) {
  cat("✅ Archivos generados:\n")
  for(archivo_gen in archivos_salida) {
    cat("   -", archivo_gen, "\n")
  }
  
  # Buscar específicamente archivos XML
  archivos_xml <- archivos_salida[grepl("\\.xml$", archivos_salida)]
  
  if(length(archivos_xml) > 0) {
    cat("\n🎯 ARCHIVOS XML PARA MOODLE:\n")
    for(xml in archivos_xml) {
      cat("   📄", xml, "\n")
    }
    
    cat("\n✅ ÉXITO: Archivos listos para importar en Moodle\n")
    cat("📋 INSTRUCCIONES:\n")
    cat("1. Ve a: Ejemplos-Pedagogicos/salida/\n")
    cat("2. Descarga el archivo .xml\n")
    cat("3. En Moodle: Banco de preguntas → Importar\n")
    cat("4. Selecciona el archivo .xml\n")
    cat("5. Formato: XML de Moodle\n")
    cat("6. ¡Importar!\n")
    
  } else {
    cat("⚠️ No se generaron archivos XML\n")
    cat("   Pero se generaron otros archivos para diagnóstico\n")
  }
  
} else {
  cat("❌ No se generaron archivos\n")
  cat("   Revisa los errores mostrados arriba\n")
}

cat("\n" %R% 60, "\n")
cat("🏁 PROCESO COMPLETADO\n")
cat("=" %R% 60, "\n")
