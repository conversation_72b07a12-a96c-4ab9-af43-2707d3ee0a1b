# =============================================================================
# SOLUCIÓN PARA ERROR: "el argumento 'file' está ausente"
# Error común al intentar usar "Knit" con archivos R-exams
# =============================================================================

# 🚨 PROBLEMA IDENTIFICADO:
# El botón "Knit" de RStudio NO funciona con exams2moodle()
# Debes ejecutar el código desde la CONSOLA DE R

# =============================================================================
# PASO 1: VERIFICAR E INSTALAR LIBRERÍAS NECESARIAS
# =============================================================================

cat("🔍 VERIFICANDO LIBRERÍAS NECESARIAS...\n")

# Lista de librerías requeridas
librerias_necesarias <- c("exams", "knitr", "rmarkdown")

# Verificar qué librerías faltan
librerias_faltantes <- librerias_necesarias[!librerias_necesarias %in% rownames(installed.packages())]

if(length(librerias_faltantes) > 0) {
  cat("📦 INSTALANDO LIBRERÍAS FALTANTES:", paste(librerias_faltantes, collapse = ", "), "\n")
  install.packages(librerias_faltantes)
} else {
  cat("✅ TODAS LAS LIBRERÍAS ESTÁN INSTALADAS\n")
}

# Cargar librerías
library(exams)
library(knitr)
library(rmarkdown)

cat("✅ LIBRERÍAS CARGADAS EXITOSAMENTE\n\n")

# =============================================================================
# PASO 2: CONFIGURAR DIRECTORIO DE TRABAJO
# =============================================================================

cat("📁 CONFIGURANDO DIRECTORIO DE TRABAJO...\n")

# Verificar directorio actual
directorio_actual <- getwd()
cat("Directorio actual:", directorio_actual, "\n")

# Verificar si estamos en el directorio correcto del proyecto
if(!dir.exists("Ejemplos-Pedagogicos")) {
  cat("⚠️  ADVERTENCIA: No se encuentra el directorio 'Ejemplos-Pedagogicos'\n")
  cat("   Asegúrate de estar en el directorio raíz del proyecto\n")
  cat("   Directorio actual:", directorio_actual, "\n")
  stop("❌ Directorio incorrecto")
} else {
  cat("✅ DIRECTORIO CORRECTO DETECTADO\n")
}

# Cambiar al directorio de ejemplos pedagógicos
setwd("Ejemplos-Pedagogicos")
cat("📂 Cambiado a directorio: Ejemplos-Pedagogicos\n\n")

# =============================================================================
# PASO 3: LISTAR ARCHIVOS .RMD DISPONIBLES
# =============================================================================

cat("📋 ARCHIVOS .RMD DISPONIBLES:\n")
archivos_rmd <- list.files(pattern = "\\.Rmd$", full.names = FALSE)

if(length(archivos_rmd) == 0) {
  stop("❌ No se encontraron archivos .Rmd en el directorio")
}

for(i in 1:length(archivos_rmd)) {
  cat(sprintf("%d. %s\n", i, archivos_rmd[i]))
}
cat("\n")

# =============================================================================
# PASO 4: FUNCIÓN PARA GENERAR EXAMEN MOODLE
# =============================================================================

generar_examen_moodle <- function(archivo_rmd, num_versiones = 3) {
  
  cat("🎯 GENERANDO EXAMEN MOODLE...\n")
  cat("Archivo:", archivo_rmd, "\n")
  cat("Versiones:", num_versiones, "\n\n")
  
  # Verificar que el archivo existe
  if(!file.exists(archivo_rmd)) {
    stop("❌ Error: No se encuentra el archivo ", archivo_rmd)
  }
  
  # Crear directorio de salida si no existe
  if(!dir.exists("salida")) {
    dir.create("salida")
    cat("📁 Directorio 'salida' creado\n")
  }
  
  # Generar nombre base para los archivos de salida
  nombre_base <- gsub("\\.Rmd$", "", basename(archivo_rmd))
  nombre_base <- gsub("^[0-9]+-", "", nombre_base)  # Remover prefijo numérico
  
  cat("📝 Nombre base para archivos:", nombre_base, "\n")
  
  # Configurar semilla para reproducibilidad
  set.seed(123)
  
  tryCatch({
    # GENERAR EXAMEN MOODLE
    exams2moodle(
      archivo_rmd,
      n = num_versiones,
      name = nombre_base,
      encoding = "UTF-8",
      dir = "salida",
      
      # Configuraciones básicas
      converter = "pandoc-mathjax",
      base64 = FALSE,
      
      # Configuraciones para preguntas de selección múltiple
      mchoice = list(
        shuffle = TRUE,
        answernumbering = "ABCD"
      ),
      
      # Configuraciones para preguntas cloze
      cloze = list(
        eval = list(
          partial = FALSE,
          rule = "none"
        )
      )
    )
    
    cat("✅ EXAMEN GENERADO EXITOSAMENTE\n")
    cat("📁 Archivos generados en: Ejemplos-Pedagogicos/salida/\n")
    
    # Listar archivos generados
    archivos_generados <- list.files("salida", pattern = paste0(nombre_base, ".*\\.xml$"))
    cat("📄 Archivos XML generados:\n")
    for(archivo in archivos_generados) {
      cat("   -", archivo, "\n")
    }
    
    return(TRUE)
    
  }, error = function(e) {
    cat("❌ ERROR AL GENERAR EXAMEN:\n")
    cat("   ", as.character(e), "\n")
    return(FALSE)
  })
}

# =============================================================================
# PASO 5: FUNCIÓN PARA DIAGNÓSTICO COMPLETO
# =============================================================================

diagnostico_completo <- function(archivo_rmd) {
  
  cat("🔍 EJECUTANDO DIAGNÓSTICO COMPLETO...\n")
  cat("Archivo a diagnosticar:", archivo_rmd, "\n\n")
  
  # 1. Verificar existencia del archivo
  if(!file.exists(archivo_rmd)) {
    cat("❌ ARCHIVO NO ENCONTRADO:", archivo_rmd, "\n")
    return(FALSE)
  }
  cat("✅ Archivo encontrado\n")
  
  # 2. Verificar sintaxis R Markdown
  tryCatch({
    contenido <- readLines(archivo_rmd, warn = FALSE)
    cat("✅ Archivo legible (", length(contenido), "líneas)\n")
  }, error = function(e) {
    cat("❌ Error al leer archivo:", as.character(e), "\n")
    return(FALSE)
  })
  
  # 3. Verificar chunks de R
  if(any(grepl("```\\{r", contenido))) {
    cat("✅ Chunks de R detectados\n")
  } else {
    cat("⚠️  No se detectaron chunks de R\n")
  }
  
  # 4. Verificar metainformación
  if(any(grepl("extype:", contenido))) {
    cat("✅ Metainformación de exams detectada\n")
  } else {
    cat("⚠️  No se detectó metainformación de exams\n")
  }
  
  # 5. Intentar compilación de prueba
  cat("\n🧪 INTENTANDO COMPILACIÓN DE PRUEBA...\n")
  
  tryCatch({
    # Intentar generar una versión de prueba
    temp_dir <- tempdir()
    exams2html(archivo_rmd, n = 1, dir = temp_dir, name = "test")
    cat("✅ Compilación de prueba exitosa\n")
    return(TRUE)
  }, error = function(e) {
    cat("❌ Error en compilación de prueba:\n")
    cat("   ", as.character(e), "\n")
    return(FALSE)
  })
}

# =============================================================================
# PASO 6: EJEMPLOS DE USO
# =============================================================================

cat("🚀 EJEMPLOS DE USO:\n")
cat("=" %R% 50, "\n")

cat("\n1️⃣  GENERAR EXAMEN SIMPLE:\n")
cat('generar_examen_moodle("03-pregunta_secuencial_geometria.Rmd")\n')

cat("\n2️⃣  GENERAR MÚLTIPLES VERSIONES:\n")
cat('generar_examen_moodle("06-adopcion_mascotas_secuencial_anti_mecanica.Rmd", 5)\n')

cat("\n3️⃣  DIAGNÓSTICO DE ARCHIVO:\n")
cat('diagnostico_completo("04-pregunta_cloze_estadistica_compleja.Rmd")\n')

cat("\n4️⃣  GENERAR TODOS LOS ARCHIVOS:\n")
cat("for(archivo in archivos_rmd) {\n")
cat("  cat('\\n📝 Procesando:', archivo, '\\n')\n")
cat("  generar_examen_moodle(archivo, 2)\n")
cat("}\n")

# =============================================================================
# PASO 7: EJECUCIÓN AUTOMÁTICA (OPCIONAL)
# =============================================================================

cat("\n🤖 EJECUCIÓN AUTOMÁTICA DISPONIBLE:\n")
cat("¿Deseas generar automáticamente el primer archivo disponible? (s/n): ")

# Descomentar las siguientes líneas para ejecución automática
# respuesta <- readline()
# if(tolower(respuesta) == "s" && length(archivos_rmd) > 0) {
#   cat("\n🚀 GENERANDO AUTOMÁTICAMENTE:", archivos_rmd[1], "\n")
#   generar_examen_moodle(archivos_rmd[1])
# }

cat("\n" %R% 70, "\n")
cat("✅ SCRIPT DE SOLUCIÓN LISTO\n")
cat("📋 INSTRUCCIONES:\n")
cat("1. Ejecuta este script completo en la CONSOLA de R\n")
cat("2. NO uses el botón 'Knit' de RStudio\n")
cat("3. Usa las funciones generar_examen_moodle() o diagnostico_completo()\n")
cat("4. Los archivos XML se generarán en 'Ejemplos-Pedagogicos/salida/'\n")
cat("5. Importa los archivos XML en Moodle\n")
cat("=" %R% 70, "\n")
